﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfRemainingDebtDal : EfCompanyEntityRepositoryBase<RemainingDebt, GymContext>, IRemainingDebtDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfRemainingDebtDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        // Backward compatibility constructor
        public EfRemainingDebtDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }

        public List<RemainingDebtDetailDto> GetRemainingDebtDetails()
        {
            using (var context = new GymContext())
            {
                // Mevcut kullanıcının şirket ID'sini al
                int companyId = _companyContext.GetCompanyId();

                var result = from rd in context.RemainingDebts
                             join p in context.Payments on rd.PaymentID equals p.PaymentID
                             join ms in context.Memberships on p.MemberShipID equals ms.MembershipID
                             join m in context.Members on ms.MemberID equals m.MemberID
                             where rd.IsActive && rd.RemainingAmount > 0 
                             && rd.CompanyID == companyId // Şirket ID'sine göre filtrele
                             && p.CompanyID == companyId // Ödemelerin de aynı şirkete ait olduğundan emin ol
                             && ms.CompanyID == companyId // Üyeliklerin de aynı şirkete ait olduğundan emin ol
                             && m.CompanyID == companyId // Üyelerin de aynı şirkete ait olduğundan emin ol
                             select new RemainingDebtDetailDto
                             {
                                 RemainingDebtID = rd.RemainingDebtID,
                                 PaymentID = rd.PaymentID,
                                 MemberName = m.Name,
                                 PhoneNumber = m.PhoneNumber,
                                 OriginalAmount = rd.OriginalAmount,
                                 RemainingAmount = rd.RemainingAmount,
                                 LastUpdateDate = rd.LastUpdateDate,
                                 PaymentMethod = p.PaymentMethod
                             };
                return result.ToList();
            }
        }
    }
}
