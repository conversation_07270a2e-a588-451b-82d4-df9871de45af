﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfLicenseTransactionDal : EfEntityRepositoryBase<LicenseTransaction, GymContext>, ILicenseTransactionDal
    {
        // Constructor injection (Scalability için)
        public EfLicenseTransactionDal(GymContext context) : base(context)
        {
        }

        // Backward compatibility constructor
        public EfLicenseTransactionDal() : base()
        {
        }
    }

}
