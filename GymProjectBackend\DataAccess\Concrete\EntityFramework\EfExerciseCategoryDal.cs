using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfExerciseCategoryDal : EfEntityRepositoryBase<ExerciseCategory, GymContext>, IExerciseCategoryDal
    {
        // Constructor injection (Scalability için)
        public EfExerciseCategoryDal(GymContext context) : base(context)
        {
        }

        // Backward compatibility constructor
        public EfExerciseCategoryDal() : base()
        {
        }

        public List<ExerciseCategoryDto> GetAllCategories()
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                var result = from ec in _context.ExerciseCategories
                             select new ExerciseCategoryDto
                             {
                                 ExerciseCategoryID = ec.ExerciseCategoryID,
                                 CategoryName = ec.CategoryName,
                                 Description = ec.Description,
                                 IsActive = ec.IsActive,
                                 CreationDate = ec.CreationDate
                             };
                return result.ToList();
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    var result = from ec in context.ExerciseCategories
                                 select new ExerciseCategoryDto
                                 {
                                     ExerciseCategoryID = ec.ExerciseCategoryID,
                                     CategoryName = ec.CategoryName,
                                     Description = ec.Description,
                                     IsActive = ec.IsActive
                                 };
                    return result.ToList();
                }
            }
        }

        public List<ExerciseCategoryDto> GetActiveCategories()
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                var result = from ec in _context.ExerciseCategories
                             where ec.IsActive == true
                             orderby ec.CategoryName
                             select new ExerciseCategoryDto
                             {
                                 ExerciseCategoryID = ec.ExerciseCategoryID,
                                 CategoryName = ec.CategoryName,
                                 Description = ec.Description,
                                 IsActive = ec.IsActive,
                                 CreationDate = ec.CreationDate
                             };
                return result.ToList();
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    var result = from ec in context.ExerciseCategories
                                 where ec.IsActive == true
                                 orderby ec.CategoryName
                                 select new ExerciseCategoryDto
                                 {
                                     ExerciseCategoryID = ec.ExerciseCategoryID,
                                     CategoryName = ec.CategoryName,
                                     Description = ec.Description,
                                     IsActive = ec.IsActive,
                                     CreationDate = ec.CreationDate
                                 };
                    return result.ToList();
                }
            }
        }
    }
}
